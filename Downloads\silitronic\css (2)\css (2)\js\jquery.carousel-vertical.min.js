(function(e,t,n,s){"use strict";var i={items:1,margin:0,nav:!0,navText:["prev","next"]};e.fn.extend({carouselVertical:function(a){var c=function(i,a){function c(){if(g.hasClass("cv-loaded"))return!1;if(E=g.find(".item"),M=E.length,0===M)return!1;if(L=g.height(),M<a.items&&(a.items=M),w=parseInt((L-a.margin*a.items)/a.items),x=w+a.margin,x*M,C=M-a.items,T=-C*x,g.wrapInner('<div class="cv-stage-outer"><div class="cv-stage"></div></div>'),g.addClass("cv-drag"),g.height(L),E.wrap('<div class="cv-item"></div>'),E=g.find(".cv-item"),p=g.find(".cv-stage"),E.css({height:w+"px","margin-bottom":a.margin+"px"}).slice(0,a.items).addClass("active"),g.addClass("cv-loaded"),i.addEventListener("touchstart",r,!0),i.addEventListener("MSPointerDown",r,!0),i.addEventListener("touchmove",r,!0),i.addEventListener("MSPointerMove",r,!0),i.addEventListener("touchend",r,!0),i.addEventListener("MSPointerUp",r,!0),i.addEventListener("touchcancel",r,!0),i.addEventListener("mousedown",d,!1),i.addEventListener("mousemove",d,!1),i.addEventListener("mouseup",d,!1),a.nav){g.prepend('<div class="cv-nav"><div class="cv-prev">'+a.navText[0]+'</div><div class="cv-next">'+a.navText[1]+"</div></div>");let e=g.find(".cv-prev")[0],t=g.find(".cv-next")[0];e.addEventListener("touchend",r,!0),e.addEventListener("MSPointerUp",r,!0),e.addEventListener("click",f,!1),t.addEventListener("touchend",r,!0),t.addEventListener("MSPointerUp",r,!0),t.addEventListener("click",h,!1)}return g.on("goTo",l),m(0),!0}function d(e){let t=e.clientY-k;switch(e.type){case"mousedown":P=!0,k=t;break;case"mousemove":P&&v(t);break;case"mouseup":default:P&&(t>0&&(t=0),o(t),P=!1)}}function r(e){let s=e.changedTouches[0],i=n.createEvent("MouseEvent"),a=new Date,c=null;switch(e.type){case"touchstart":c="mousedown",Y=a.getTime();break;case"touchmove":c="mousemove",Y=-1;break;case"touchend":Y>-1&&a.getTime()-Y<S?(Y=-1,c="click"):c="mouseup";break;case"touchcancel":default:return}i.initMouseEvent(c,!0,!0,t,1,s.screenX,s.screenY,s.clientX,s.clientY,!1,!1,!1,!1,0,null),s.target.dispatchEvent(i),e.preventDefault()}function v(e){e>0&&(e/=5),e<T&&(e=T+(e-T)/5),p.css("transition","none"),g.addClass("cv-grab"),u(e)}function o(e){p.css("transition","all 0.25s ease"),g.removeClass("cv-grab"),m(Math.round(-e/x))}function u(e){p.css("transform","translateY("+e+"px)")}function l(t,n){n!==s&&e.isNumeric(n)&&Math.floor(n)===n&&m(n-1)}function m(e){e<0&&(e=0),e>C&&(e=C),b=e,E.removeClass("active"),E.slice(e,a.items+e).addClass("active"),k=-x*e,u(k)}function f(){m(b-1)}function h(){m(b+1)}var g=e(i),p=null,E=null,L=0,w=0,x=0,M=0,b=1,k=1,C=1,T=1,P=!1,S=100,Y=-1;if(!c())return!1};for(let t=0;t<this.length;t++){var d=e.extend({},i,a);c(this[t],d)}return this}})})(jQuery,window,document);
//# sourceMappingURL=jquery.carousel-vertical.min.js.map